# TwoFactor插件代码质量改进建议

## 概述

本文档针对TwoFactor插件的代码质量问题，提供详细的改进建议，包括代码结构优化、性能提升、可维护性增强和测试覆盖等方面。

## 1. 代码结构优化

### 1.1 服务层抽取

**当前问题**：业务逻辑耦合在控制器中，违反单一职责原则

**改进方案**：引入服务层架构

```php
// 创建服务层基类
abstract class BaseService {
    protected $logger;
    protected $cache;
    
    public function __construct() {
        $this->logger = service('logger');
        $this->cache = cache();
    }
    
    protected function logInfo($message, $context = []) {
        $this->logger->info($message, $context);
    }
    
    protected function logError($message, $context = []) {
        $this->logger->error($message, $context);
    }
}

// 双因子认证服务
class TwoFactorAuthService extends BaseService {
    private $verification_model;
    private $settings_model;
    private $email_service;
    
    public function __construct() {
        parent::__construct();
        $this->verification_model = new TwoFactor_verification_model();
        $this->settings_model = new TwoFactor_settings_model();
        $this->email_service = new EmailService();
    }
    
    public function sendVerificationCode($user) {
        try {
            // 检查是否已有有效验证码
            if ($this->hasValidCode($user->email)) {
                return ['success' => true, 'message' => '验证码已发送，请检查邮箱'];
            }
            
            // 生成新验证码
            $code = $this->generateSecureCode();
            
            // 保存验证码
            $this->saveVerificationCode($code, $user);
            
            // 发送邮件
            $result = $this->email_service->sendVerificationEmail($user, $code);
            
            if ($result) {
                $this->logInfo('验证码发送成功', ['user_id' => $user->id]);
                return ['success' => true, 'message' => '验证码已发送'];
            } else {
                $this->logError('验证码发送失败', ['user_id' => $user->id]);
                return ['success' => false, 'message' => '发送失败，请重试'];
            }
            
        } catch (Exception $e) {
            $this->logError('发送验证码异常', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'message' => '系统错误，请联系管理员'];
        }
    }
    
    public function verifyCode($user_id, $code) {
        try {
            $verification = $this->findValidVerification($user_id, $code);
            
            if (!$verification) {
                $this->logInfo('验证码验证失败', ['user_id' => $user_id]);
                return ['success' => false, 'message' => '验证码无效或已过期'];
            }
            
            // 删除已使用的验证码
            $this->verification_model->delete_permanently($verification->id);
            
            $this->logInfo('验证码验证成功', ['user_id' => $user_id]);
            return ['success' => true, 'message' => '验证成功'];
            
        } catch (Exception $e) {
            $this->logError('验证码验证异常', [
                'user_id' => $user_id,
                'error' => $e->getMessage()
            ]);
            return ['success' => false, 'message' => '验证失败，请重试'];
        }
    }
}
```

### 1.2 配置管理优化

**当前问题**：配置获取效率低，缺少默认值处理

**改进方案**：实现配置管理器

```php
class TwoFactorConfigManager {
    private static $instance = null;
    private $config_cache = [];
    private $cache_ttl = 3600;
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private $default_config = [
        'twofactor_email_subject' => '确认您的登录',
        'twofactor_code_length' => 6,
        'twofactor_code_expiry' => 900, // 15分钟
        'twofactor_max_attempts' => 5,
        'twofactor_lockout_duration' => 900,
        'twofactor_enable_totp' => false,
        'twofactor_enable_sms' => false,
    ];
    
    public function get($key, $default = null) {
        // 先检查缓存
        if (isset($this->config_cache[$key])) {
            return $this->config_cache[$key];
        }
        
        // 从数据库获取
        $settings_model = new TwoFactor_settings_model();
        $value = $settings_model->get_setting($key);
        
        // 如果没有值，使用默认配置
        if ($value === null) {
            $value = $default ?? $this->default_config[$key] ?? null;
        }
        
        // 缓存结果
        $this->config_cache[$key] = $value;
        
        return $value;
    }
    
    public function set($key, $value) {
        $settings_model = new TwoFactor_settings_model();
        $result = $settings_model->save_setting($key, $value);
        
        // 更新缓存
        if ($result) {
            $this->config_cache[$key] = $value;
            // 清除相关缓存
            cache()->delete("twofactor_config_{$key}");
        }
        
        return $result;
    }
    
    public function clearCache() {
        $this->config_cache = [];
    }
}

// 简化的配置获取函数
function get_twofactor_config($key, $default = null) {
    return TwoFactorConfigManager::getInstance()->get($key, $default);
}
```

### 1.3 异常处理标准化

**当前问题**：错误处理不统一，缺少自定义异常

**改进方案**：实现异常处理体系

```php
// 自定义异常类
class TwoFactorException extends Exception {
    protected $error_code;
    protected $user_message;
    
    public function __construct($message, $user_message = null, $error_code = 0, $previous = null) {
        parent::__construct($message, $error_code, $previous);
        $this->user_message = $user_message ?? $message;
        $this->error_code = $error_code;
    }
    
    public function getUserMessage() {
        return $this->user_message;
    }
    
    public function getErrorCode() {
        return $this->error_code;
    }
}

class CodeExpiredException extends TwoFactorException {}
class CodeInvalidException extends TwoFactorException {}
class TooManyAttemptsException extends TwoFactorException {}
class ConfigurationException extends TwoFactorException {}

// 异常处理器
class TwoFactorExceptionHandler {
    public static function handle($exception) {
        $logger = service('logger');
        
        if ($exception instanceof TwoFactorException) {
            $logger->warning('TwoFactor异常', [
                'message' => $exception->getMessage(),
                'code' => $exception->getErrorCode(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ]);
            
            return [
                'success' => false,
                'message' => $exception->getUserMessage(),
                'error_code' => $exception->getErrorCode()
            ];
        } else {
            $logger->error('TwoFactor系统异常', [
                'message' => $exception->getMessage(),
                'trace' => $exception->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '系统错误，请联系管理员',
                'error_code' => 500
            ];
        }
    }
}
```

## 2. 性能优化

### 2.1 数据库查询优化

**当前问题**：查询效率低，缺少索引

**改进方案**：优化查询和索引

```php
class OptimizedTwoFactorVerificationModel extends Crud_model {
    protected $table = 'twofactor_verification';
    
    public function __construct() {
        parent::__construct($this->table);
        $this->addIndexes();
    }
    
    private function addIndexes() {
        // 确保必要的索引存在
        $indexes = [
            'idx_code' => 'code',
            'idx_created_at' => 'created_at',
            'idx_deleted' => 'deleted'
        ];
        
        foreach ($indexes as $name => $column) {
            $this->ensureIndexExists($name, $column);
        }
    }
    
    public function findValidCode($code, $email) {
        $sql = "SELECT v.* FROM {$this->table} v 
                WHERE v.code = ? 
                AND v.params LIKE ? 
                AND v.deleted = 0 
                AND v.created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
                ORDER BY v.created_at DESC 
                LIMIT 1";
        
        $expiry = get_twofactor_config('twofactor_code_expiry', 900);
        return $this->db->query($sql, [$code, "%{$email}%", $expiry])->getRow();
    }
    
    public function cleanExpiredCodes() {
        $expiry = get_twofactor_config('twofactor_code_expiry', 900);
        $sql = "DELETE FROM {$this->table} 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? SECOND)";
        
        return $this->db->query($sql, [$expiry]);
    }
    
    public function getUserActiveCodesCount($email) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} 
                WHERE params LIKE ? 
                AND deleted = 0 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)";
        
        $expiry = get_twofactor_config('twofactor_code_expiry', 900);
        $result = $this->db->query($sql, ["%{$email}%", $expiry])->getRow();
        
        return $result ? $result->count : 0;
    }
}
```

### 2.2 缓存策略实现

```php
class TwoFactorCacheService {
    private $cache;
    private $prefix = 'twofactor_';
    
    public function __construct() {
        $this->cache = cache();
    }
    
    public function getUserSettings($user_id) {
        $key = $this->prefix . "user_settings_{$user_id}";
        $settings = $this->cache->get($key);
        
        if (!$settings) {
            $settings = $this->loadUserSettingsFromDB($user_id);
            $this->cache->save($key, $settings, 3600); // 1小时缓存
        }
        
        return $settings;
    }
    
    public function setAttemptCount($identifier, $count) {
        $key = $this->prefix . "attempts_{$identifier}";
        $this->cache->save($key, $count, 3600);
    }
    
    public function getAttemptCount($identifier) {
        $key = $this->prefix . "attempts_{$identifier}";
        return $this->cache->get($key) ?: 0;
    }
    
    public function setLockout($identifier, $duration) {
        $key = $this->prefix . "lockout_{$identifier}";
        $this->cache->save($key, time() + $duration, $duration);
    }
    
    public function isLockedOut($identifier) {
        $key = $this->prefix . "lockout_{$identifier}";
        $lockout_time = $this->cache->get($key);
        return $lockout_time && $lockout_time > time();
    }
    
    public function clearUserCache($user_id) {
        $keys = [
            $this->prefix . "user_settings_{$user_id}",
            $this->prefix . "attempts_{$user_id}",
            $this->prefix . "lockout_{$user_id}"
        ];
        
        foreach ($keys as $key) {
            $this->cache->delete($key);
        }
    }
}
```

## 3. 代码质量提升

### 3.1 输入验证和数据清理

```php
class TwoFactorValidator {
    public static function validateCode($code) {
        if (empty($code)) {
            throw new CodeInvalidException('验证码不能为空');
        }
        
        if (!is_numeric($code)) {
            throw new CodeInvalidException('验证码必须是数字');
        }
        
        $length = get_twofactor_config('twofactor_code_length', 6);
        if (strlen($code) !== $length) {
            throw new CodeInvalidException("验证码必须是{$length}位数字");
        }
        
        return true;
    }
    
    public static function validateEmail($email) {
        if (empty($email)) {
            throw new TwoFactorException('邮箱地址不能为空');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new TwoFactorException('邮箱地址格式无效');
        }
        
        return true;
    }
    
    public static function sanitizeInput($input, $type = 'string') {
        switch ($type) {
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            case 'string':
            default:
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        }
    }
}
```

### 3.2 日志记录标准化

```php
class TwoFactorLogger {
    private $logger;
    private $context_prefix = 'TwoFactor';
    
    public function __construct() {
        $this->logger = service('logger');
    }
    
    public function logAuthAttempt($user_id, $method, $success, $details = []) {
        $context = array_merge([
            'component' => $this->context_prefix,
            'user_id' => $user_id,
            'method' => $method,
            'success' => $success,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ], $details);
        
        if ($success) {
            $this->logger->info('2FA认证成功', $context);
        } else {
            $this->logger->warning('2FA认证失败', $context);
        }
    }
    
    public function logConfigChange($user_id, $setting, $old_value, $new_value) {
        $this->logger->info('2FA配置变更', [
            'component' => $this->context_prefix,
            'user_id' => $user_id,
            'setting' => $setting,
            'old_value' => $old_value,
            'new_value' => $new_value,
            'ip' => $this->getClientIP()
        ]);
    }
    
    public function logSecurityEvent($event_type, $details = []) {
        $context = array_merge([
            'component' => $this->context_prefix,
            'event_type' => $event_type,
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIP()
        ], $details);
        
        $this->logger->warning('2FA安全事件', $context);
    }
    
    private function getClientIP() {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
               $_SERVER['HTTP_X_REAL_IP'] ?? 
               $_SERVER['REMOTE_ADDR'] ?? 
               'Unknown';
    }
}
```

### 3.3 单元测试框架

```php
// 测试基类
abstract class TwoFactorTestCase extends \CodeIgniter\Test\CIUnitTestCase {
    protected $twofactor_service;
    protected $mock_user;
    
    protected function setUp(): void {
        parent::setUp();
        
        $this->twofactor_service = new TwoFactorAuthService();
        $this->mock_user = (object)[
            'id' => 1,
            'email' => '<EMAIL>',
            'first_name' => 'Test',
            'last_name' => 'User'
        ];
    }
    
    protected function tearDown(): void {
        // 清理测试数据
        $this->cleanupTestData();
        parent::tearDown();
    }
    
    protected function cleanupTestData() {
        // 清理测试过程中创建的数据
        $db = db_connect();
        $db->query("DELETE FROM twofactor_verification WHERE params LIKE '%<EMAIL>%'");
    }
}

// 具体测试类
class TwoFactorAuthServiceTest extends TwoFactorTestCase {
    public function testSendVerificationCodeSuccess() {
        $result = $this->twofactor_service->sendVerificationCode($this->mock_user);
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('验证码已发送', $result['message']);
    }
    
    public function testVerifyValidCode() {
        // 先发送验证码
        $this->twofactor_service->sendVerificationCode($this->mock_user);
        
        // 获取生成的验证码（测试环境下）
        $code = $this->getLastGeneratedCode($this->mock_user->email);
        
        // 验证验证码
        $result = $this->twofactor_service->verifyCode($this->mock_user->id, $code);
        
        $this->assertTrue($result['success']);
    }
    
    public function testVerifyInvalidCode() {
        $result = $this->twofactor_service->verifyCode($this->mock_user->id, '000000');
        
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('验证码无效', $result['message']);
    }
    
    private function getLastGeneratedCode($email) {
        $db = db_connect();
        $query = $db->query(
            "SELECT code FROM twofactor_verification 
             WHERE params LIKE ? 
             ORDER BY created_at DESC LIMIT 1", 
            ["%{$email}%"]
        );
        
        $result = $query->getRow();
        return $result ? $result->code : null;
    }
}
```

## 4. 代码规范和文档

### 4.1 代码注释标准

```php
/**
 * 双因子认证服务类
 * 
 * 提供完整的双因子认证功能，包括验证码生成、发送、验证等
 * 
 * @package TwoFactor\Services
 * <AUTHOR> Name
 * @version 1.0
 * @since   2024-01-01
 */
class TwoFactorAuthService extends BaseService {
    
    /**
     * 发送验证码给用户
     * 
     * @param object $user 用户对象，必须包含id、email、first_name、last_name属性
     * @return array 包含success和message的结果数组
     * 
     * @throws TwoFactorException 当用户信息无效时抛出异常
     * @throws ConfigurationException 当邮件配置错误时抛出异常
     * 
     * @example
     * $result = $service->sendVerificationCode($user);
     * if ($result['success']) {
     *     echo $result['message'];
     * }
     */
    public function sendVerificationCode($user) {
        // 方法实现...
    }
    
    /**
     * 验证用户输入的验证码
     * 
     * @param int    $user_id 用户ID
     * @param string $code    用户输入的验证码
     * @return array 验证结果
     * 
     * @throws CodeInvalidException 当验证码格式无效时抛出异常
     * @throws CodeExpiredException 当验证码已过期时抛出异常
     */
    public function verifyCode($user_id, $code) {
        // 方法实现...
    }
}
```

### 4.2 API文档生成

```php
/**
 * @api {post} /twofactor/authenticate 验证双因子认证码
 * @apiName AuthenticateTwoFactor
 * @apiGroup TwoFactor
 * @apiVersion 1.0.0
 * 
 * @apiDescription 验证用户输入的双因子认证码
 * 
 * @apiParam {String} twofactor_code 6位数字验证码
 * 
 * @apiSuccess {Boolean} success 请求是否成功
 * @apiSuccess {String} message 响应消息
 * 
 * @apiSuccessExample {json} 成功响应:
 * {
 *   "success": true,
 *   "message": "验证成功"
 * }
 * 
 * @apiError {Boolean} success 固定为false
 * @apiError {String} message 错误消息
 * 
 * @apiErrorExample {json} 错误响应:
 * {
 *   "success": false,
 *   "message": "验证码无效或已过期"
 * }
 */
public function authenticate() {
    // 方法实现...
}
```

## 5. 实施建议

### 5.1 重构步骤
1. **第一阶段**：创建服务层，逐步迁移业务逻辑
2. **第二阶段**：实现异常处理体系和日志记录
3. **第三阶段**：优化数据库查询和缓存机制
4. **第四阶段**：添加单元测试和集成测试
5. **第五阶段**：完善文档和代码注释

### 5.2 质量保证
- 代码审查流程
- 自动化测试
- 性能监控
- 安全扫描

### 5.3 维护计划
- 定期代码重构
- 性能优化
- 安全更新
- 功能增强

---

**文档版本**：1.0
**适用版本**：TwoFactor 1.0+
**维护周期**：建议每季度审查一次
