<?php

$lang["language_locale"]      = "zh"; //locale code
$lang["language_locale_long"] = "zh-CN"; //long locale code
/* NOTE: DO NOT CHANGE THIS FILE. IF YOU WANT TO UPDATE THE LANGUAGE THEN COPY THIS FILE TO custom_lang.php AND UPDATE THERE */

$lang['flexiblebackup']                          = '弹性备份';
$lang['Flexiblebackup']                          = '弹性备份';
$lang['existing_backups']                        = '已有备份';
$lang['next_scheduled_backup']                   = '定时备份';
$lang['Flexiblebackup_settings']                 = '备份设置';
$lang['backup_now']                              = '立即备份';
$lang['backup_pro']                              = '备份专业版';
$lang['backup_date']                             = '备份日期';
$lang['click_on_download']                       = '下载';
$lang['uploaded_to_remote_storage']              = '已上传到远程存储';
$lang['upload_to_remote']                        = '上传到远程';
$lang['restore'] 				                 = '恢复';
$lang['database'] 					             = '数据库';
$lang['nothing_currently_scheduled']             = '暂无定时任务';
$lang['time_now'] 					             = '立刻';
$lang['include_database_in_the_backup']          = '在备份中包含数据库（所有表数据、结构和存储过程）';
$lang['include_file_in_the_backup']              = '在备份中包含文件（可选择具体的文件类型和目录）';
$lang['flexiblebackup_app']                      = '应用程序（配置文件、控制器、模型、视图、语言文件，app 目录）';
$lang['flexiblebackup_plugins']                 = '插件扩展（所有已安装的插件及其数据文件，plugins 目录）';
$lang['flexiblebackup_assets']                  = '静态资源（CSS样式、JS脚本、图片、字体文件，assets 目录）';
$lang['flexiblebackup_system']                  = '系统文件（CodeIgniter核心文件和系统库，system 目录）';
$lang['flexiblebackup_files'] 		         = '用户文件（项目文件、上传文件、个人资料图片等，files 目录）';
$lang['flexiblebackup_documentation'] 			 = '文档资料（系统文档、API文档、帮助文件，documentation 目录）';
$lang['flexiblebackup_writable'] 				 = '可写目录（日志、缓存、会话、临时文件，writable 目录）';
$lang['flexiblebackup_other'] 					 = '其他文件（根目录配置文件如.env、index.php等，不包含install目录）';
$lang['flexiblebackup'] 					     = '灵活备份';
$lang['existing_backups'] 					     = '已有备份';
$lang['next_scheduled_backup'] 				     = '下次计划';
$lang['backup'] 							     = '执行备份';
$lang['files_backup_schedule'] 			         = '文件备份计划（自动备份系统文件的时间间隔，需要配置服务器定时任务）';
$lang['database_backup_schedule'] 		         = '数据库备份计划（自动备份数据库的时间间隔，需要配置服务器定时任务）';
$lang['backup_name_prefix'] 			         = '备份文件名前缀（用于标识备份文件，建议使用项目名称）';
$lang['choose_your_remote_storage']              = '选择远程存储方式（支持FTP、SFTP、Amazon S3、WebDAV和邮件，点击图标选择）';

// Settings options
$lang['type_manual']                            = '手动';
$lang['type_every_two_hours']                   = '每2小时';
$lang['type_every_four_hours']                  = '每4小时';
$lang['type_every_eight_hours']                 = '每8小时';
$lang['type_every_twelve_hours']                = '每12小时';
$lang['type_daily']                             = '每天';
$lang['type_weekly']                            = '每周';
$lang['type_fortnightly']                       = '每两周';
$lang['type_monthly']                           = '每月';

// Remote storage options
$lang['ftp_storage']                            = 'FTP';
$lang['s3_storage']                             = 'Amazon S3';
$lang['sftp_storage']                           = 'SFTP/SCP';
$lang['webdav_storage']                         = 'WebDAV';
$lang['email']                                  = '邮件';

$lang['s3_description']                         = '请从AWS控制台获取访问密钥和私有密钥，然后为Amazon S3存储桶设置一个全球唯一的名称（仅限字母和数字）。';
$lang['save_changes']                           = '保存设置';
$lang['email_notes']                            = '请注意：邮件服务器通常有文件大小限制（一般为10-20MB），超出限制的备份文件可能无法发送成功。';

$lang['ftp_server']                             = 'FTP服务器地址';
$lang['ftp_user']                               = 'FTP用户名';
$lang['ftp_password']                           = 'FTP登录密码';
$lang['ftp_path']                               = 'FTP存储路径（目录必须存在且可写）';
$lang['sftp_server']                            = 'SFTP服务器地址';
$lang['sftp_user']                              = 'SFTP用户名';
$lang['sftp_password']                          = 'SFTP登录密码';
$lang['sftp_path']                              = 'SFTP存储路径（目录必须存在且可写）';
$lang['s3_access_key']                          = 'Amazon S3访问密钥';
$lang['s3_secret_key']                          = 'Amazon S3私有密钥';
$lang['s3_location']                            = 'Amazon S3存储桶名称';
$lang['s3_region']                              = 'Amazon S3区域（如：us-east-1）';
$lang['webdav_base_uri']                        = 'WebDAV服务器地址';
$lang['webdav_username']                        = 'WebDAV用户名';
$lang['webdav_password']                        = 'WebDAV登录密码';
$lang['email_address']                                  = '邮箱地址';
$lang['include_in_files_backup']                        = '选择文件备份内容（可多选，建议根据实际需要选择）：';
$lang['view_log']                                       = '查看操作日志（显示备份和恢复的详细过程）';
$lang['restore_files_from']                             = '从以下备份恢复文件（请确认备份完整性）：';
$lang['choose_component_to_restore'] 	 		        = '选择要恢复的组件（可多选，恢复前请确认当前数据已备份）：';
$lang['restore_warning']                                = '⚠️ 重要警告：恢复操作将完全覆盖当前网站的相应内容！包括应用程序配置、静态资源、用户文件、插件数据、系统文件、数据库等。此操作不可逆，请务必在恢复前备份当前数据。';
$lang['restore_db_warning']                             = '<b>🔴 数据库恢复风险提示：</b>数据库恢复将完全替换当前所有数据，包括用户信息、项目数据、系统设置等。强烈建议在恢复前先导出当前数据库，以防数据丢失。';
$lang['log_file']                                       = '操作日志';
$lang['download_log_file']                              = '下载日志文件';
$lang['backup_restored_successfully']                   = '备份恢复成功';
$lang['backup_file_not_found']                          = '备份文件不存在';
$lang['file_ready_actions']                             = '文件操作选项：';
$lang['download_to_your_computer']                      = '下载到本地';
$lang['delete_from_your_webserver']                     = '从服务器删除';
$lang['browse_contents']                                = '浏览内容';
$lang['database_restoration_failed']                    = '数据库恢复失败';
$lang['database_has_been_restored_successfully']        = '数据库恢复成功';
$lang['backup_not_found']                               = '备份不存在';
$lang['backup_folder_not_found']                        = '备份目录不存在';
$lang['email_message_1']                                = '您好！<br /><br />请查看附件中的备份文件';
$lang['email_message_2']                                = '，创建时间：';
$lang['email_message_3']                                = '。<br /><br />祝好！';
$lang['new_backup_available']                           = '新备份已生成';
$lang['include_in_database_backup']                     = '自动备份时包含数据库（包括所有表结构、数据和存储过程）';
$lang['set_time_for_scheduled_backup']                  = '设置定时备份时间（24小时制，需要在服务器配置cron定时任务）';
$lang['auto_backup_to_remote_enabled']			        = '自动上传定时备份到远程存储（备份完成后立即上传到选定的远程位置）';
$lang['file_uploaded_succesfully']			            = '文件上传成功（已保存到远程存储）';
$lang['please_select_files']                            = '请选择要备份的文件类型（至少选择一项，可多选）';
$lang['please_select_backup_type']                      = '请选择备份类型（数据库备份或文件备份，或两者都选）';
$lang['backup_successfully']                            = '备份操作完成';
$lang['least_one_file_type_to_restore']                 = '请至少选择一种文件类型进行恢复';
$lang['settings_updated_successfully']			        = '设置保存成功';
$lang['verify_your_remote_settings']  			        = '远程存储设置有误，请<b>点击此处</b>验证';
$lang['database_and_files_backup']                      = '数据库与文件备份';
$lang['backup']                                         = '备份';
$lang['database_backup']                                = '数据库备份';
$lang['files_backup']                                   = '文件备份';
$lang['google_drive']                                   = 'Google Drive';
$lang['inherit_default_credentials']                    = '使用RISE CRM默认凭据';
$lang['sftp_port']                                      = 'SFTP端口号';
$lang['ftp_port']                                       = 'FTP端口号';
$lang['delete_this_file']                               = '删除文件';
$lang['please_verify_your_email_settings']              = '请验证邮箱设置';
$lang['no_longer_exists']                               = '文件已不存在';
$lang['email_storage']                                  = '邮件存储';
$lang['selected_remote_storage']                        = '已选择的远程存储';

// 备份页面优化相关翻译
$lang['backup_page_description']                     = '创建系统数据库和文件的完整备份，确保数据安全。您可以选择备份类型和具体的文件内容。';
$lang['backup_type_selection']                       = '备份类型选择';
$lang['database_backup_description']                 = '备份所有数据库表、数据和存储过程';
$lang['files_backup_description']                    = '备份系统文件、用户文件和配置文件';
$lang['file_backup_options']                         = '文件备份选项';
$lang['select_file_types_description']               = '请选择要包含在文件备份中的具体内容类型：';
$lang['backup_progress']                             = '备份进度';
$lang['backup_operation_note']                       = '备份操作可能需要几分钟时间，请耐心等待。';
$lang['start_backup']                                = '开始备份';
$lang['backing_up']                                  = '正在备份';
$lang['preparing_backup']                            = '正在准备备份...';
$lang['backing_up_database']                         = '正在备份数据库...';
$lang['backing_up_files']                            = '正在备份文件...';
$lang['compressing_files']                           = '正在压缩文件...';
$lang['finalizing_backup']                           = '正在完成备份...';
$lang['backup_completed']                            = '备份已完成';
$lang['reset']                                       = '重置';
$lang['please_select_backup_type']                  = '请选择备份的类型（数据库备份或文件备份），或两者都选择';

return $lang;
