<?php
// Flexiblebackup 插件内部导航标签页
$flexiblebackup_menu = array(
    "backup_management" => array(
        array("name" => "existing_backups", "url" => "flexiblebackup/settings/existing_backups"),
        array("name" => "next_scheduled_backup", "url" => "flexiblebackup/settings/next_scheduled_backup"),
        array("name" => "backup", "url" => "flexiblebackup/backup"),
    ),
    "backup_settings" => array(
        array("name" => "settings", "url" => "flexiblebackup/settings/settings"),
    ),
);

// 确定当前活动的标签页
$current_url = uri_string();
$active_tab = "";

foreach ($flexiblebackup_menu as $category => $items) {
    foreach ($items as $item) {
        if (strpos($current_url, $item["url"]) !== false) {
            $active_tab = $item["name"];
            break 2;
        }
    }
}

// 如果没有匹配到，默认为 existing_backups
if (empty($active_tab)) {
    $active_tab = "existing_backups";
}
?>

<ul class="nav nav-tabs vertical settings d-block" role="tablist">
    <?php
    foreach ($flexiblebackup_menu as $key => $value) {
        // 检查当前分类是否包含活动标签页
        $collapse_in = "";
        $collapsed_class = "collapsed";
        if (in_array($active_tab, array_column($value, "name"))) {
            $collapse_in = "show";
            $collapsed_class = "";
        }
    ?>
        <div class="clearfix settings-anchor <?php echo $collapsed_class; ?>" data-bs-toggle="collapse" data-bs-target="#flexiblebackup-tab-<?php echo $key; ?>">
            <?php echo app_lang($key); ?>
        </div>

        <?php
        echo "<div id='flexiblebackup-tab-$key' class='collapse $collapse_in' data-bs-parent='#flexiblebackup-menu-accordion'>";
        echo "<ul class='list-group help-catagory'>";

        foreach ($value as $sub_setting) {
            $active_class = "";
            $setting_name = get_array_value($sub_setting, "name");
            $setting_url = get_array_value($sub_setting, "url");

            if ($active_tab == $setting_name) {
                $active_class = "active";
            }

            echo "<a href='" . get_uri($setting_url) . "' class='list-group-item $active_class'>" . app_lang($setting_name) . "</a>";
        }

        echo "</ul>";
        echo "</div>";
    }
    ?>
</ul>
