<?php

defined('PLUGINPATH') || exit('No direct script access allowed');

/*
  Plugin Name: 灵活备份 Flexible Backup & Restore Module
  Plugin URL: 
  Description: 具有自动计划功能的全面备份和恢复的插件。</br>A comprehensive backup and restore module with automatic scheduling feature
  Version: 1.2.0
  Requires at least: 3.5.2
  Author: theWise Team
  Author URL: https://www.thewise.com
 */
define('BACKUP_FOLDER', FCPATH.'flexiblebackup'.'/');

require_once __DIR__.'/vendor/autoload.php';
require_once __DIR__.'/Libraries/Apiinit.php';
require_once __DIR__.'/Libraries/Backup.php';
require_once __DIR__.'/Libraries/BackupRemote.php';
require_once __DIR__.'/Libraries/ManageBackup.php';
require_once __DIR__.'/Libraries/SqlScriptParser.php';

use Flexiblebackup\Libraries\Apiinit;
use Flexiblebackup\Libraries\Backup;




// 添加设置菜单组到系统设置，与插件扩展同级
app_hooks()->add_filter('app_filter_admin_settings_menu', function ($settings_menu) {
    $settings_menu["flexiblebackup"] = array(
        array("name" => "backup", "url" => "flexiblebackup/backup"),        
        array("name" => "existing_backups", "url" => "flexiblebackup/settings/existing_backups"),
        array("name" => "next_scheduled_backup", "url" => "flexiblebackup/settings/next_scheduled_backup"),
        array("name" => "settings", "url" => "flexiblebackup/settings/settings"),
    );
    return $settings_menu;
});

//install dependencies
register_installation_hook('Flexiblebackup', function ($item_purchase_code) {
    include PLUGINPATH.'Flexiblebackup/install/do_install.php';
});

register_uninstallation_hook('Flexiblebackup', function () {
    $dbprefix = get_db_prefix();
    $db = db_connect('default');

    $sql_query = 'DELETE FROM `'.$dbprefix.'settings` WHERE `'.$dbprefix."settings`.`setting_name` IN ('Flexiblebackup_verification_id', 'Flexiblebackup_last_verification', 'Flexiblebackup_product_token', 'Flexiblebackup_heartbeat');";
    $db->query($sql_query);
});

app_hooks()->add_action('app_hook_head_extension', function () {
    echo '
        <link href="'.base_url(PLUGIN_URL_PATH.'Flexiblebackup/assets/css/tree.css?v='.get_setting('app_version')).'"  rel="stylesheet" type="text/css" />
    ';
});

app_hooks()->add_action('app_hook_layout_main_view_extension', function () {
    echo '
        <script src="'.base_url(PLUGIN_URL_PATH.'Flexiblebackup/assets/js/tree.js?v='.get_setting('app_version')).'"></script>
    ';
});

app_hooks()->add_action('app_hook_after_cron_run', function () {
    $backup_lib = new Backup();
    $backup_lib->runScheduledBackups();
});
