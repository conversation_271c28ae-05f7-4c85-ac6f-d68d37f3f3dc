<?php
$file_options = getBackupFileOptions();
$icons = [
    'plugins' => 'package',
    'app' => 'code',
    'files' => 'file-text',
    'assets' => 'image',
    'system' => 'cpu',
    'documentation' => 'book',
    'writable' => 'edit-3',
    'others' => 'more-horizontal'
];

// 语言键值映射 - 将备份文件夹名称映射到语言键值
$lang_keys = [
    'plugins' => 'flexiblebackup_plugins',
    'app' => 'flexiblebackup_app',
    'files' => 'flexiblebackup_files',
    'assets' => 'flexiblebackup_assets',
    'system' => 'flexiblebackup_system',
    'documentation' => 'flexiblebackup_documentation',
    'writable' => 'flexiblebackup_writable',
    'others' => 'flexiblebackup_other'
];

// 设置键值映射 - 将备份文件夹名称映射到设置键值
$setting_keys = [
    'plugins' => 'include_flexiblebackup_plugins',
    'app' => 'include_flexiblebackup_app',
    'files' => 'include_flexiblebackup_files',
    'assets' => 'include_flexiblebackup_assets',
    'system' => 'include_flexiblebackup_system',
    'documentation' => 'include_flexiblebackup_documentation',
    'writable' => 'include_flexiblebackup_writable',
    'others' => 'include_flexiblebackup_other'
];

foreach ($file_options as $key => $value) {
    $icon = $icons[$value] ?? 'folder';
    $lang_key = $lang_keys[$value] ?? $value;
    $setting_key = $setting_keys[$value] ?? 'include_'.$value;
    $col_class = (count($file_options) <= 4) ? 'col-md-6' : 'col-md-4';
?>
    <div class="<?php echo $col_class; ?> mb-3">
        <div class="file-option-card p-3 border rounded h-100">
            <div class="form-check">
                <input type="checkbox" class="form-check-input" name="include_<?php echo $value; ?>" id="include_<?php echo $value; ?>" value="1" <?php echo (1 == get_setting($setting_key)) ? 'checked' : ''; ?> />
                <label class="form-check-label w-100" for="include_<?php echo $value; ?>">
                    <div class="d-flex align-items-start">
                        <i data-feather="<?php echo $icon; ?>" class="icon-16 text-secondary me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <div class="fw-bold text-dark"><?php echo app_lang($lang_key); ?></div>
                        </div>
                    </div>
                </label>
            </div>
        </div>
    </div>
<?php } ?>