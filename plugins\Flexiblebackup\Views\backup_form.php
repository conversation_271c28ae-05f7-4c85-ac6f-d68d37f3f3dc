<div id="page-content" class="page-wrapper clearfix">
    <div class="row">
        <div class="col-sm-3 col-lg-2">
            <?php
            $tab_view['active_tab'] = 'backup';
            echo view("settings/tabs", $tab_view);
            ?>
        </div>

        <div class="col-sm-9 col-lg-10">
            <!-- 页面标题和说明 -->
            <div class="page-title clearfix mb-3">
                <h1><i data-feather="download-cloud" class="icon-24 text-primary me-2"></i><?php echo app_lang($title); ?></h1>
                <p class="text-muted"><?php echo app_lang('backup_page_description'); ?></p>
            </div>

            <?php echo form_open(get_uri('flexiblebackup/perform_backup'), ['id' => 'backupForm', 'class' => 'general-form', 'role' => 'form']); ?>

            <!-- 备份类型选择 -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i data-feather="settings" class="icon-16 me-2"></i><?php echo app_lang('backup_type_selection'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 数据库备份选项 -->
                        <div class="col-md-6">
                            <div class="backup-option-card p-3 border rounded h-100" id="database-option" data-target="includeDatabase">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" value="1" class="form-check-input me-3" id="includeDatabase" name="include_database_in_the_backup">
                                    <i data-feather="database" class="icon-20 text-info me-3"></i>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo app_lang('database_backup'); ?></h6>
                                        <small class="text-muted"><?php echo app_lang('database_backup_description'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 文件备份选项 -->
                        <div class="col-md-6">
                            <div class="backup-option-card p-3 border rounded h-100" id="files-option" data-target="includeFile">
                                <div class="d-flex align-items-center">
                                    <input type="checkbox" value="1" class="form-check-input me-3" id="includeFile" name="include_file_in_the_backup">
                                    <i data-feather="folder" class="icon-20 text-warning me-3"></i>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo app_lang('files_backup'); ?></h6>
                                        <small class="text-muted"><?php echo app_lang('files_backup_description'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件备份详细选项 -->
            <div class="card shadow-sm mb-4 backup-options hide" id="file-options-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i data-feather="folder-plus" class="icon-16 me-2"></i><?php echo app_lang('file_backup_options'); ?></h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3"><?php echo app_lang('select_file_types_description'); ?></p>
                    <div class="row">
                        <?php echo view('Flexiblebackup\Views\backup_files_options'); ?>
                    </div>
                </div>
            </div>

            <!-- 备份进度区域 -->
            <div class="card shadow-sm mb-4 hide" id="backup-progress-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i data-feather="activity" class="icon-16 me-2"></i><?php echo app_lang('backup_progress'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" id="backup-progress-bar">
                            <span id="progress-text">0%</span>
                        </div>
                    </div>
                    <div id="backup-status" class="text-center">
                        <i data-feather="clock" class="icon-16 me-1"></i><?php echo app_lang('preparing_backup'); ?>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">
                                <i data-feather="info" class="icon-14 me-1"></i>
                                <?php echo app_lang('backup_operation_note'); ?>
                            </small>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" id="resetButton">
                                <i data-feather="refresh-cw" class="icon-16 me-1"></i><?php echo app_lang('reset'); ?>
                            </button>
                            <button type="button" class="btn btn-primary btn-lg" id="backupButton">
                                <i data-feather="download-cloud" class="icon-16 me-1"></i><?php echo app_lang('start_backup'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <?php echo form_close(); ?>
        </div>
    </div>
</div>
<style>
    .backup-option-card {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid #dee2e6 !important;
        user-select: none;
    }

    .backup-option-card:hover {
        border-color: #007bff !important;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.075) !important;
        transform: translateY(-1px);
    }

    .backup-option-card.selected {
        border-color: #007bff !important;
        background-color: #f8f9ff;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 123, 255, 0.15) !important;
    }

    .backup-option-card input[type="checkbox"] {
        transform: scale(1.2);
        margin-right: 0.75rem;
    }

    .file-option-card {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .file-option-card:hover {
        border-color: #6c757d !important;
        background-color: #f8f9fa;
    }

    .file-option-card.selected {
        border-color: #28a745 !important;
        background-color: #f8fff9;
    }

    .progress {
        border-radius: 10px;
    }

    .progress-bar {
        border-radius: 10px;
    }

    .rotating {
        animation: rotate 2s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .page-title h1 {
        font-weight: 600;
        color: #2c3e50;
    }

    .card {
        border: none;
        border-radius: 12px;
    }

    .card-header {
        border-radius: 12px 12px 0 0 !important;
        border-bottom: none;
        font-weight: 600;
    }

    .btn-lg {
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 8px;
    }

    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .shadow-sm {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    }
</style>

<script>
    $(function() {
        // 备份选项卡片点击效果 - 使用事件委托和更精确的选择器
        $(document).on('click', '.backup-option-card', function(e) {
            // 如果点击的是checkbox本身，不处理（避免双重触发）
            if ($(e.target).is('input[type="checkbox"]')) {
                return;
            }

            const targetId = $(this).data('target');
            const checkbox = $('#' + targetId);
            if (checkbox.length) {
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // 文件选项卡片点击效果
        $(document).on('click', '.file-option-card', function(e) {
            // 如果点击的是checkbox本身，不处理（避免双重触发）
            if ($(e.target).is('input[type="checkbox"]')) {
                return;
            }

            const checkbox = $(this).find('input[type="checkbox"]');
            if (checkbox.length) {
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });

        // 复选框状态变化时更新卡片样式
        $(document).on('change', 'input[type="checkbox"]', function() {
            const card = $(this).closest('.backup-option-card, .file-option-card');
            if ($(this).is(':checked')) {
                card.addClass('selected');
            } else {
                card.removeClass('selected');
            }
        });

        // 文件备份选项显示/隐藏
        $('#includeFile').on('change', function () {
            if ($(this).is(':checked')) {
                $('#file-options-card').removeClass('hide').hide().fadeIn(300);
            } else {
                $('#file-options-card').fadeOut(300, function() {
                    $(this).addClass('hide');
                });
            }
        });

        // 重置按钮
        $('#resetButton').on('click', function() {
            $('input[type="checkbox"]').prop('checked', false).trigger('change');
            $('#backup-progress-card').addClass('hide');
            $('#backupButton').prop('disabled', false).html('<i data-feather="download-cloud" class="icon-16 me-1"></i><?php echo app_lang('start_backup'); ?>');
            feather.replace();
        });

        // 模拟备份进度（实际项目中应该通过AJAX获取真实进度）
        function simulateProgress() {
            let progress = 0;
            const progressBar = $('#backup-progress-bar');
            const progressText = $('#progress-text');
            const statusText = $('#backup-status');

            const statuses = [
                '<?php echo app_lang('preparing_backup'); ?>',
                '<?php echo app_lang('backing_up_database'); ?>',
                '<?php echo app_lang('backing_up_files'); ?>',
                '<?php echo app_lang('compressing_files'); ?>',
                '<?php echo app_lang('finalizing_backup'); ?>'
            ];

            const interval = setInterval(function() {
                progress += Math.random() * 20;
                if (progress > 100) progress = 100;

                progressBar.css('width', progress + '%');
                progressText.text(Math.round(progress) + '%');

                // 更新状态文本
                const statusIndex = Math.floor((progress / 100) * (statuses.length - 1));
                statusText.html('<i data-feather="activity" class="icon-16 me-1"></i>' + statuses[statusIndex]);
                feather.replace();

                if (progress >= 100) {
                    clearInterval(interval);
                    statusText.html('<i data-feather="check-circle" class="icon-16 me-1 text-success"></i><?php echo app_lang('backup_completed'); ?>');
                    feather.replace();
                }
            }, 200);
        }

        // 表单验证函数
        function validateBackupForm() {
            const databaseChecked = $('#includeDatabase').is(':checked');
            const filesChecked = $('#includeFile').is(':checked');

            console.log('Backup validation - Database:', databaseChecked, 'Files:', filesChecked);

            if (!databaseChecked && !filesChecked) {
                console.log('Validation failed: No backup type selected');
                appAlert.error('<?php echo app_lang('please_select_backup_type'); ?>', {duration: 4000});
                return false;
            }

            console.log('Validation passed');
            return true;
        }

        // 添加按钮点击事件监听器，验证后手动提交表单
        $("#backupButton").on('click', function(e) {
            console.log('Backup button clicked');

            if (!validateBackupForm()) {
                console.log('Validation failed, not submitting form');
                return false;
            }

            console.log('Validation passed, submitting form');
            // 手动触发表单提交
            $("#backupForm").submit();
        });

        // 初始化表单
        window.backupForm = $("#backupForm").appForm({
            isModal: false,
            onSubmit: function () {
                console.log('appForm onSubmit called');

                // 显示进度卡片
                $('#backup-progress-card').removeClass('hide').hide().fadeIn(300);

                // 禁用按钮并更改文本
                $("#backupButton").prop('disabled', true).html('<i data-feather="loader" class="icon-16 me-1 rotating"></i><?php echo app_lang('backing_up'); ?>...');
                feather.replace();

                // 开始模拟进度（在实际项目中，这应该通过WebSocket或定期AJAX调用来获取真实进度）
                // simulateProgress();

                return true; // 允许提交
            },
            onSuccess: function (result) {
                // 隐藏进度卡片
                $('#backup-progress-card').fadeOut(300, function() {
                    $(this).addClass('hide');
                });

                // 恢复按钮状态
                $("#backupButton").prop('disabled', false).html('<i data-feather="download-cloud" class="icon-16 me-1"></i><?php echo app_lang('start_backup'); ?>');
                feather.replace();

                // 显示成功消息
                appAlert.success(result.message, {duration: 4000});

                // 重置表单
                $('input[type="checkbox"]').prop("checked", false).trigger('change');

                // 如果存在备份表格，刷新它
                if ($('#existingBackupTable').length) {
                    $('#existingBackupTable').appTable({reload:true});
                }
            },
            onError: function(result) {
                // 隐藏进度卡片
                $('#backup-progress-card').fadeOut(300, function() {
                    $(this).addClass('hide');
                });

                // 恢复按钮状态
                $("#backupButton").prop('disabled', false).html('<i data-feather="download-cloud" class="icon-16 me-1"></i><?php echo app_lang('start_backup'); ?>');
                feather.replace();

                // 显示错误消息
                appAlert.error(result.message, {duration: 4000});
            }
        });

        // 初始化页面时设置已选中项的样式
        $('input[type="checkbox"]:checked').each(function() {
            $(this).closest('.backup-option-card, .file-option-card').addClass('selected');
        });

        // 初始化Feather图标
        feather.replace();
    });
</script>
